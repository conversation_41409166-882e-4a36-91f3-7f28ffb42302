package limetray

import (
	"github.com/nutanalabs/pos-gateway/internal/posclients/adapter"
	"github.com/nutanalabs/pos-gateway/internal/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// LimeTrayAdapter implements the POSAdapter interface for LimeTray
type LimeTrayAdapter struct {
	client *Client
}

func (p *LimeTrayAdapter) SendMenuProcessingRequestStatus(status common.MenuProcessingStatus, url string) (types.SuccessResponse, error) {
	_, err := p.client.SendMenuProcessingRequestStatus(status, url)
	return types.SuccessResponse{}, err
}

// NewLimeTrayAdapter creates a new LimeTray adapter instance
func NewLimeTrayAdapter(client *Client) adapter.POSAdapter {
	return &LimeTrayAdapter{
		client: client,
	}
}

// SendOrder sends order details to LimeTray
func (p *LimeTrayAdapter) SendOrder(order orders.Order) (types.SuccessResponse, error) {
	transformedOrder := TransformOrder(order)
	_, err := p.client.SendOrder(transformedOrder)
	if err != nil {
		return types.SuccessResponse{}, err
	}
	return types.SuccessResponse{
		Message: "Order sent to LimeTray successfully",
	}, nil
}

// SendRiderDetails sends rider details to LimeTray
func (p *LimeTrayAdapter) SendRiderDetails(rider orders.Rider) (types.SuccessResponse, error) {
	transformedRider := TransformRider(rider)
	_, err := p.client.SendRiderDetails(transformedRider)
	return types.SuccessResponse{}, err
}
