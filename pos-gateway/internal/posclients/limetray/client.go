package limetray

import (
	"net/http"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// Client represents the LimeTray API client
type Client struct {
	config      *config.Config
	httpClient  *http.Client
	utilsClient utils.HTTPClient
}

// NewClient creates a new LimeTray API client
func NewClient(cfg *config.Config, httpClient *http.Client,
	utilsClient utils.HTTPClient) *Client {
	return &Client{
		config:      cfg,
		httpClient:  httpClient,
		utilsClient: utilsClient,
	}
}

// SendOrder sends order details to LimeTray API
func (c *Client) SendOrder(order interface{}) (interface{}, error) {
	// For now, this is a mock implementation since we don't have the actual LimeTray API URL configured
	// In a real implementation, you would configure the LimeTray API URL in the config file
	// and make the actual HTTP request here

	// Mock successful response
	response := map[string]interface{}{
		"status":  "success",
		"message": "Order sent to LimeTray successfully",
		"order":   order,
	}

	return response, nil

	// Commented out the actual HTTP call until proper URL is configured:
	/*
		limeTrayURL := "https://api.limetray.com/v1/orders" // This should come from config
		res, err := c.utilsClient.Post(utils.HTTPPayload{
			Client:  c.httpClient,
			URL:     limeTrayURL,
			Body:    order,
			Timeout: time.Second * 30,
		})
		if err != nil {
			return nil, err
		}
		return res, nil
	*/
}

// SendRiderDetails sends rider details to LimeTray API
func (c *Client) SendRiderDetails(rider interface{}) (interface{}, error) {
	// For now, this is a mock implementation since we don't have the actual LimeTray API URL configured
	// In a real implementation, you would configure the LimeTray API URL in the config file
	// and make the actual HTTP request here

	// Mock successful response
	response := map[string]interface{}{
		"status":  "success",
		"message": "Rider details sent to LimeTray successfully",
		"rider":   rider,
	}

	return response, nil
}

// SendMenuProcessingRequestStatus sends menu processing status to LimeTray API
func (c *Client) SendMenuProcessingRequestStatus(status interface{}, url string) (interface{}, error) {
	// Mock implementation for now
	response := map[string]interface{}{
		"status":  "success",
		"message": "Menu processing status sent to LimeTray successfully",
	}
	return response, nil
}
