package limetray

import "fmt"

// Config represents LimeTray API configuration
type Config struct {
	BaseURL      string `json:"baseUrl"`
	APIKey       string `json:"apiKey"`
	APISecret    string `json:"apiSecret"`
	RestaurantID string `json:"restaurantId"`
	Timeout      int    `json:"timeout"` // in seconds
	RetryCount   int    `json:"retryCount"`
	Environment  string `json:"environment"` // "sandbox", "production"
}

// APIEndpoints represents LimeTray API endpoints
type APIEndpoints struct {
	Menu        string `json:"menu"`
	Order       string `json:"order"`
	OrderStatus string `json:"orderStatus"`
	RiderStatus string `json:"riderStatus"`
	ItemStock   string `json:"itemStock"`
	StoreStatus string `json:"storeStatus"`
	Callback    string `json:"callback"`
}

// DefaultConfig returns default configuration for LimeTray
func DefaultConfig() *Config {
	return &Config{
		BaseURL:     "https://api.limetray.com/v1",
		Timeout:     30,
		RetryCount:  3,
		Environment: "sandbox",
	}
}

// DefaultEndpoints returns default API endpoints for LimeTray
func DefaultEndpoints() *APIEndpoints {
	return &APIEndpoints{
		Menu:        "/menu",
		Order:       "/order",
		OrderStatus: "/order/status",
		RiderStatus: "/rider/status",
		ItemStock:   "/inventory/stock",
		StoreStatus: "/store/status",
		Callback:    "/callback",
	}
}

// Validate validates the LimeTray configuration
func (c *Config) Validate() error {
	if c.BaseURL == "" {
		return ErrMissingBaseURL
	}
	if c.APIKey == "" {
		return ErrMissingAPIKey
	}
	if c.APISecret == "" {
		return ErrMissingAPISecret
	}
	if c.RestaurantID == "" {
		return ErrMissingRestaurantID
	}
	return nil
}

// Configuration errors
var (
	ErrMissingBaseURL      = fmt.Errorf("base URL is required")
	ErrMissingAPIKey       = fmt.Errorf("API key is required")
	ErrMissingAPISecret    = fmt.Errorf("API secret is required")
	ErrMissingRestaurantID = fmt.Errorf("restaurant ID is required")
)
