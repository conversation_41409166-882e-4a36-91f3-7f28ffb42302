package limetray

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posgateway/handler/transformers"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
	"github.com/nutanalabs/pos-gateway/internal/utils"
)

// TransformOrderFromTransformer transforms unified order to LimeTray order format using centralized transformation
func TransformOrderFromTransformer(order orders.Order) interface{} {
	// Use the service layer transformer for consistent transformation
	utilsImpl := utils.NewUtils()
	transformer := transformers.NewTransformers(utilsImpl)

	// Transform to LimeTray format using struct-based approach
	transformedOrder := transformer.TransformUnifiedOrderToProviderOrder(&order, constants.LimeTrayClientId)

	return transformedOrder
}

// TransformRiderFromTransformer transforms unified rider to LimeTray rider format using centralized transformation
func TransformRiderFromTransformer(rider orders.Rider) interface{} {
	// TODO: Implement LimeTray rider transformation
	return rider
}

// TransformOrder transforms unified order to LimeTray order format (legacy function for backward compatibility)
func TransformOrder(order orders.Order) interface{} {
	return TransformOrderFromTransformer(order)
}

// TransformRider transforms unified rider to LimeTray rider format (legacy function for backward compatibility)
func TransformRider(rider orders.Rider) interface{} {
	return TransformRiderFromTransformer(rider)
}
