package limetray

// MenuPayload represents the LimeTray menu API payload
type MenuPayload struct {
	RestaurantID string     `json:"restaurantId"`
	Menu         MenuData   `json:"menu"`
	CallbackURL  string     `json:"callbackUrl"`
	Timestamp    int64      `json:"timestamp"`
}

// MenuData represents the menu data structure for LimeTray API
type MenuData struct {
	Categories []Category `json:"categories"`
	Taxes      []Tax      `json:"taxes"`
	Charges    []Charge   `json:"charges"`
	Timings    []Timing   `json:"timings"`
}

// Category represents a menu category for LimeTray API
type Category struct {
	CategoryID    string     `json:"categoryId"`
	CategoryName  string     `json:"categoryName"`
	Rank          int        `json:"rank"`
	ProductList   []Product  `json:"productList,omitempty"`
	SubCategories []Category `json:"subCategories,omitempty"`
	IsActive      bool       `json:"isActive"`
}

// Product represents a product for LimeTray API
type Product struct {
	ProductID      string       `json:"productId"`
	ProductType    string       `json:"productType"`
	ProductName    string       `json:"productName"`
	Description    string       `json:"description,omitempty"`
	ProductSkuList []ProductSku `json:"productSkuList"`
	IsActive       bool         `json:"isActive"`
	ImageURL       string       `json:"imageUrl,omitempty"`
}

// ProductSku represents a product SKU for LimeTray API
type ProductSku struct {
	ProductSkuID    string       `json:"productSkuId"`
	SkuName         string       `json:"skuName,omitempty"`
	OutOfStock      bool         `json:"outOfStock"`
	ProductSkuPrice float64      `json:"productSkuPrice"`
	Addons          []AddonGroup `json:"addons,omitempty"`
	Services        []Service    `json:"services,omitempty"`
	IsActive        bool         `json:"isActive"`
}

// AddonGroup represents an addon group for LimeTray API
type AddonGroup struct {
	AddonGroupID   string  `json:"addonGroupId"`
	AddonGroupName string  `json:"addonGroupName"`
	Min            int     `json:"min"`
	Max            int     `json:"max"`
	Addons         []Addon `json:"addons"`
	IsRequired     bool    `json:"isRequired"`
}

// Addon represents an individual addon for LimeTray API
type Addon struct {
	AddonID    string  `json:"addonId"`
	AddonName  string  `json:"addonName"`
	AddonPrice float64 `json:"addonPrice"`
	OutOfStock bool    `json:"outOfStock"`
	IsActive   bool    `json:"isActive"`
}

// Service represents a service for LimeTray API
type Service struct {
	ServiceID   string  `json:"serviceId"`
	ServiceName string  `json:"serviceName"`
	ServiceType string  `json:"serviceType"` // "tax", "charge", "discount"
	Value       float64 `json:"value"`
	IsActive    bool    `json:"isActive"`
}

// Tax represents a tax for LimeTray API
type Tax struct {
	TaxChargeID   string  `json:"taxChargeId"`
	TaxChargeName string  `json:"taxChargeName"`
	TaxValue      float64 `json:"taxValue"`
	TaxType       string  `json:"taxType"` // "FIXED", "PERCENTAGE"
	IsActive      bool    `json:"isActive"`
}

// Charge represents a charge for LimeTray API
type Charge struct {
	TaxChargeID   string  `json:"taxChargeId"`
	TaxChargeName string  `json:"taxChargeName"`
	TaxValue      float64 `json:"taxValue"`
	TaxType       string  `json:"taxType"` // "FIXED", "PERCENTAGE"
	IsActive      bool    `json:"isActive"`
}

// Timing represents timing information for LimeTray API
type Timing struct {
	Day       string `json:"day"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
	Available bool   `json:"available"`
}

// MenuResponse represents the response from LimeTray menu API
type MenuResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	ErrorCode string `json:"errorCode,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}
