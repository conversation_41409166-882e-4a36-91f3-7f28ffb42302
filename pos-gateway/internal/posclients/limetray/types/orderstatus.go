package limetray

// OrderStatusUpdate represents order status update for LimeTray API
type OrderStatusUpdate struct {
	OrderID       string `json:"orderId"`
	RestaurantID  string `json:"restaurantId"`
	Status        string `json:"status"`
	Timestamp     int64  `json:"timestamp"`
	EstimatedTime int    `json:"estimatedTime,omitempty"` // in minutes
	Reason        string `json:"reason,omitempty"`
	CallbackURL   string `json:"callbackUrl,omitempty"`
}

// OrderStatusResponse represents the response from LimeTray order status API
type OrderStatusResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	OrderID   string `json:"orderId,omitempty"`
	Status    string `json:"status,omitempty"`
	ErrorCode string `json:"errorCode,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}

// OrderStatusHistory represents order status history for LimeTray API
type OrderStatusHistory struct {
	OrderID    string              `json:"orderId"`
	StatusList []OrderStatusEntry  `json:"statusList"`
}

// OrderStatusEntry represents a single status entry in order history
type OrderStatusEntry struct {
	Status        string `json:"status"`
	Timestamp     int64  `json:"timestamp"`
	EstimatedTime int    `json:"estimatedTime,omitempty"`
	Reason        string `json:"reason,omitempty"`
	UpdatedBy     string `json:"updatedBy,omitempty"`
}
