package limetray

// RiderStatusUpdate represents rider status update for LimeTray API
type RiderStatusUpdate struct {
	OrderID      string  `json:"orderId"`
	RestaurantID string  `json:"restaurantId"`
	RiderInfo    Rider   `json:"riderInfo"`
	Status       string  `json:"status"`
	Timestamp    int64   `json:"timestamp"`
	CallbackURL  string  `json:"callbackUrl,omitempty"`
}

// Rider represents rider information for LimeTray API
type Rider struct {
	RiderID     string  `json:"riderId"`
	RiderName   string  `json:"riderName"`
	RiderPhone  string  `json:"riderPhone"`
	VehicleType string  `json:"vehicleType"`
	VehicleNumber string `json:"vehicleNumber"`
	Location    *RiderLocation `json:"location,omitempty"`
}

// RiderLocation represents rider's current location
type RiderLocation struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
	Timestamp int64   `json:"timestamp"`
}

// RiderStatusResponse represents the response from LimeTray rider status API
type RiderStatusResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	OrderID   string `json:"orderId,omitempty"`
	RiderID   string `json:"riderId,omitempty"`
	Status    string `json:"status,omitempty"`
	ErrorCode string `json:"errorCode,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}

// RiderStatusHistory represents rider status history for LimeTray API
type RiderStatusHistory struct {
	OrderID    string              `json:"orderId"`
	RiderID    string              `json:"riderId"`
	StatusList []RiderStatusEntry  `json:"statusList"`
}

// RiderStatusEntry represents a single status entry in rider history
type RiderStatusEntry struct {
	Status    string         `json:"status"`
	Timestamp int64          `json:"timestamp"`
	Location  *RiderLocation `json:"location,omitempty"`
	UpdatedBy string         `json:"updatedBy,omitempty"`
}
