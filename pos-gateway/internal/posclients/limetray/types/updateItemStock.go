package limetray

// UpdateItemStockPayload represents the payload for updating item stock in LimeTray API
type UpdateItemStockPayload struct {
	RestaurantID string              `json:"restaurantId"`
	Items        []ItemStockUpdate   `json:"items"`
	UpdateType   string              `json:"updateType"` // "stock", "availability"
	Timestamp    int64               `json:"timestamp"`
	CallbackURL  string              `json:"callbackUrl,omitempty"`
}

// ItemStockUpdate represents an individual item stock update
type ItemStockUpdate struct {
	ProductSkuID string `json:"productSkuId"`
	OutOfStock   bool   `json:"outOfStock"`
	Available    bool   `json:"available"`
	Quantity     *int   `json:"quantity,omitempty"`
	Reason       string `json:"reason,omitempty"`
}

// BulkItemStockUpdate represents bulk item stock updates for LimeTray API
type BulkItemStockUpdate struct {
	RestaurantID string              `json:"restaurantId"`
	Updates      []ItemStockUpdate   `json:"updates"`
	BatchID      string              `json:"batchId"`
	Timestamp    int64               `json:"timestamp"`
	CallbackURL  string              `json:"callbackUrl,omitempty"`
}

// ItemStockResponse represents the response from LimeTray item stock API
type ItemStockResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	BatchID   string `json:"batchId,omitempty"`
	ErrorCode string `json:"errorCode,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	FailedItems []FailedItemUpdate `json:"failedItems,omitempty"`
}

// FailedItemUpdate represents failed item updates
type FailedItemUpdate struct {
	ProductSkuID string `json:"productSkuId"`
	ErrorCode    string `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
}

// ItemAvailabilityUpdate represents item availability update
type ItemAvailabilityUpdate struct {
	ProductSkuID string `json:"productSkuId"`
	Available    bool   `json:"available"`
	Reason       string `json:"reason,omitempty"`
	Timestamp    int64  `json:"timestamp"`
}
