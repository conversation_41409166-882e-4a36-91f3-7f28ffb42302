package limetray

// UpdateStoreStatusPayload represents the payload for updating store status in LimeTray API
type UpdateStoreStatusPayload struct {
	RestaurantID     string            `json:"restaurantId"`
	IsOpen           bool              `json:"isOpen"`
	Status           string            `json:"status"` // "open", "closed", "busy"
	DeliveryStatus   DeliveryStatus    `json:"deliveryStatus"`
	PickupStatus     PickupStatus      `json:"pickupStatus"`
	DineInStatus     DineInStatus      `json:"dineInStatus"`
	EstimatedTime    EstimatedTime     `json:"estimatedTime"`
	Timestamp        int64             `json:"timestamp"`
	Reason           string            `json:"reason,omitempty"`
	CallbackURL      string            `json:"callbackUrl,omitempty"`
}

// DeliveryStatus represents delivery service status for LimeTray API
type DeliveryStatus struct {
	Available     bool    `json:"available"`
	EstimatedTime int     `json:"estimatedTime"` // in minutes
	MinOrderValue float64 `json:"minOrderValue"`
	MaxOrderValue float64 `json:"maxOrderValue"`
	DeliveryRadius float64 `json:"deliveryRadius,omitempty"` // in km
}

// PickupStatus represents pickup service status for LimeTray API
type PickupStatus struct {
	Available     bool `json:"available"`
	EstimatedTime int  `json:"estimatedTime"` // in minutes
}

// DineInStatus represents dine-in service status for LimeTray API
type DineInStatus struct {
	Available       bool `json:"available"`
	TablesAvailable int  `json:"tablesAvailable"`
	WaitTime        int  `json:"waitTime"` // in minutes
	MaxCapacity     int  `json:"maxCapacity,omitempty"`
}

// EstimatedTime represents estimated preparation times for LimeTray API
type EstimatedTime struct {
	Delivery int `json:"delivery"` // in minutes
	Pickup   int `json:"pickup"`   // in minutes
	DineIn   int `json:"dineIn"`   // in minutes
}

// StoreStatusResponse represents the response from LimeTray store status API
type StoreStatusResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	ErrorCode string `json:"errorCode,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}

// StoreStatusHistory represents store status history for LimeTray API
type StoreStatusHistory struct {
	RestaurantID string               `json:"restaurantId"`
	StatusList   []StoreStatusEntry   `json:"statusList"`
}

// StoreStatusEntry represents a single status entry in store history
type StoreStatusEntry struct {
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
	Reason    string `json:"reason,omitempty"`
	UpdatedBy string `json:"updatedBy,omitempty"`
}
