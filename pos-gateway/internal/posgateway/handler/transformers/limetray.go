package transformers

import (
	"github.com/nutanalabs/pos-gateway/internal/constants"
	"github.com/nutanalabs/pos-gateway/internal/posclients/limetray/types"
	"github.com/nutanalabs/pos-gateway/internal/types/common"
	"github.com/nutanalabs/pos-gateway/internal/types/orders"
)

// TransformUnifiedOrderToLimeTrayOrder transforms unified order to LimeTray order format
func (t *transformers) TransformUnifiedOrderToLimeTrayOrder(order *orders.Order) *limetray.OrderPayload {
	if order == nil {
		return nil
	}

	limeTrayOrder := &limetray.OrderPayload{
		OrderID:      order.OrderInfo.OrderId,
		RestaurantID: order.OrderInfo.RestId,
		OrderType:    transformOrderType(order.OrderInfo.DeliveryMode),
		OrderStatus:  transformOrderStatus(order.OrderInfo.Status),
		Customer:     transformCustomer(order.Customer),
		Items:        transformOrderItems(order.Item),
		Payment:      transformPayment(order.Payment),
		Pricing:      transformPricing(order),
		OrderTime:    order.OrderInfo.CreatedAt,
	}

	// Add delivery info if it's a delivery order
	if order.OrderInfo.DeliveryMode == constants.FulfillmentModeDelivery {
		limeTrayOrder.DeliveryInfo = transformDeliveryInfo(order.Customer)
	}

	return limeTrayOrder
}

// transformOrderType converts unified delivery mode to LimeTray order type
func transformOrderType(deliveryMode string) string {
	switch deliveryMode {
	case constants.FulfillmentModeDelivery:
		return "delivery"
	case constants.FulfillmentModePickup:
		return "pickup"
	default:
		return "dinein"
	}
}

// transformOrderStatus converts unified order status to LimeTray order status
func transformOrderStatus(status string) string {
	switch status {
	case "placed":
		return "received"
	case "confirmed":
		return "confirmed"
	case "preparing":
		return "preparing"
	case "ready":
		return "ready"
	case "dispatched":
		return "dispatched"
	case "delivered":
		return "delivered"
	case "cancelled":
		return "cancelled"
	default:
		return "received"
	}
}

// transformCustomer converts unified customer to LimeTray customer format
func transformCustomer(customer orders.Customer) limetray.Customer {
	limeTrayCustomer := limetray.Customer{
		Name:  customer.FirstName + " " + customer.LastName,
		Phone: customer.Phone,
		Email: customer.Email,
	}

	// Add address if available
	if customer.Address != nil {
		limeTrayCustomer.Address = &limetray.Address{
			Street:     customer.Address.Street,
			City:       customer.Address.City,
			State:      customer.Address.State,
			PostalCode: customer.Address.PostalCode,
			Country:    customer.Address.Country,
			Latitude:   customer.Address.Latitude,
			Longitude:  customer.Address.Longitude,
		}
	}

	return limeTrayCustomer
}

// transformOrderItems converts unified order items to LimeTray order items format
func transformOrderItems(items []orders.Item) []limetray.OrderItem {
	if len(items) == 0 {
		return nil
	}

	limeTrayItems := make([]limetray.OrderItem, 0, len(items))
	for _, item := range items {
		limeTrayItem := limetray.OrderItem{
			ProductSkuID: item.ItemId,
			ProductName:  item.Name,
			Quantity:     item.Quantity,
			UnitPrice:    item.UnitPrice,
			TotalPrice:   float64(item.Quantity) * item.UnitPrice,
			Instructions: item.Instruction,
		}

		// Transform variants as addons
		if len(item.Variants) > 0 {
			limeTrayItem.Addons = transformVariantsToAddons(item.Variants)
		}

		// Transform addons
		if len(item.AddOns) > 0 {
			limeTrayItem.Addons = append(limeTrayItem.Addons, transformAddOns(item.AddOns)...)
		}

		// Transform services (taxes and charges)
		if len(item.Taxes) > 0 || len(item.Charges) > 0 {
			limeTrayItem.Services = transformItemServices(item.Taxes, item.Charges)
		}

		limeTrayItems = append(limeTrayItems, limeTrayItem)
	}

	return limeTrayItems
}

// transformVariantsToAddons converts variants to LimeTray addon format
func transformVariantsToAddons(variants []orders.Variant) []limetray.OrderAddon {
	if len(variants) == 0 {
		return nil
	}

	addons := make([]limetray.OrderAddon, 0, len(variants))
	for _, variant := range variants {
		addon := limetray.OrderAddon{
			AddonID:    variant.VariantId,
			AddonName:  variant.Name,
			Quantity:   variant.Quantity,
			UnitPrice:  variant.UnitPrice,
			TotalPrice: float64(variant.Quantity) * variant.UnitPrice,
		}
		addons = append(addons, addon)
	}

	return addons
}

// transformAddOns converts unified addons to LimeTray addon format
func transformAddOns(addOns []orders.AddOn) []limetray.OrderAddon {
	if len(addOns) == 0 {
		return nil
	}

	limeTrayAddons := make([]limetray.OrderAddon, 0, len(addOns))
	for _, addOn := range addOns {
		addon := limetray.OrderAddon{
			AddonID:    addOn.AddOnId,
			AddonName:  addOn.Name,
			Quantity:   addOn.Quantity,
			UnitPrice:  addOn.UnitPrice,
			TotalPrice: float64(addOn.Quantity) * addOn.UnitPrice,
		}
		limeTrayAddons = append(limeTrayAddons, addon)
	}

	return limeTrayAddons
}

// transformItemServices converts taxes and charges to LimeTray services format
func transformItemServices(taxes []orders.Tax, charges []orders.Charge) []limetray.OrderService {
	services := make([]limetray.OrderService, 0)

	// Transform taxes
	for _, tax := range taxes {
		service := limetray.OrderService{
			ServiceID:   tax.TaxId,
			ServiceName: tax.Name,
			ServiceType: "tax",
			Value:       tax.Value,
			Amount:      tax.Amount,
		}
		services = append(services, service)
	}

	// Transform charges
	for _, charge := range charges {
		service := limetray.OrderService{
			ServiceID:   charge.ChargeId,
			ServiceName: charge.Name,
			ServiceType: "charge",
			Value:       charge.Value,
			Amount:      charge.Amount,
		}
		services = append(services, service)
	}

	return services
}

// transformPayment converts unified payment to LimeTray payment format
func transformPayment(payment orders.Payment) limetray.Payment {
	return limetray.Payment{
		PaymentMethod: transformPaymentMode(payment.Mode),
		PaymentStatus: transformPaymentStatus(payment.Status),
		Amount:        payment.AmountPaid,
	}
}

// transformPaymentMode converts unified payment mode to LimeTray payment method
func transformPaymentMode(mode string) string {
	switch mode {
	case constants.PaymentModeCOD:
		return "cash"
	case constants.PaymentModeCard:
		return "card"
	case constants.PaymentModeOnline:
		return "online"
	case constants.PaymentModeWallet:
		return "wallet"
	default:
		return "cash"
	}
}

// transformPaymentStatus converts unified payment status to LimeTray payment status
func transformPaymentStatus(status string) string {
	switch status {
	case "paid":
		return "paid"
	case "pending":
		return "pending"
	case "failed":
		return "failed"
	default:
		return "pending"
	}
}

// transformPricing converts unified order pricing to LimeTray pricing format
func transformPricing(order *orders.Order) limetray.Pricing {
	pricing := limetray.Pricing{
		SubTotal:       order.OrderInfo.SubTotal,
		DeliveryCharge: order.OrderInfo.DeliveryCharge,
		PackingCharge:  order.OrderInfo.PackingCharge,
		TotalAmount:    order.OrderInfo.Total,
	}

	// Transform taxes
	if len(order.Item) > 0 {
		allTaxes := make([]limetray.OrderTax, 0)
		allCharges := make([]limetray.OrderCharge, 0)

		for _, item := range order.Item {
			// Collect taxes from items
			for _, tax := range item.Taxes {
				orderTax := limetray.OrderTax{
					TaxID:     tax.TaxId,
					TaxName:   tax.Name,
					TaxType:   "PERCENTAGE", // Default, should be determined from tax data
					TaxValue:  tax.Value,
					TaxAmount: tax.Amount,
				}
				allTaxes = append(allTaxes, orderTax)
			}

			// Collect charges from items
			for _, charge := range item.Charges {
				orderCharge := limetray.OrderCharge{
					ChargeID:     charge.ChargeId,
					ChargeName:   charge.Name,
					ChargeType:   "PERCENTAGE", // Default, should be determined from charge data
					ChargeValue:  charge.Value,
					ChargeAmount: charge.Amount,
				}
				allCharges = append(allCharges, orderCharge)
			}
		}

		pricing.Taxes = allTaxes
		pricing.Charges = allCharges
	}

	return pricing
}

// transformDeliveryInfo converts customer info to LimeTray delivery info format
func transformDeliveryInfo(customer orders.Customer) *limetray.DeliveryInfo {
	if customer.Address == nil {
		return nil
	}

	return &limetray.DeliveryInfo{
		DeliveryAddress: limetray.Address{
			Street:     customer.Address.Street,
			City:       customer.Address.City,
			State:      customer.Address.State,
			PostalCode: customer.Address.PostalCode,
			Country:    customer.Address.Country,
			Latitude:   customer.Address.Latitude,
			Longitude:  customer.Address.Longitude,
		},
	}
}
