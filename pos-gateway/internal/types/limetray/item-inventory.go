package limetray

// InventoryUpdate represents the LimeTray inventory update structure
type InventoryUpdate struct {
	RestaurantID string              `json:"restaurantId"`
	Items        []InventoryItem     `json:"items"`
	UpdateType   string              `json:"updateType"` // "stock", "availability"
	Timestamp    int64               `json:"timestamp"`
}

// InventoryItem represents an individual item inventory update
type InventoryItem struct {
	ProductSkuID string `json:"productSkuId"`
	OutOfStock   bool   `json:"outOfStock"`
	Available    bool   `json:"available"`
	Quantity     *int   `json:"quantity,omitempty"`
}

// BulkInventoryUpdate represents bulk inventory updates for LimeTray
type BulkInventoryUpdate struct {
	RestaurantID string            `json:"restaurantId"`
	Updates      []InventoryUpdate `json:"updates"`
	BatchID      string            `json:"batchId"`
}

// InventoryResponse represents the response from LimeTray inventory API
type InventoryResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	ErrorCode string `json:"errorCode,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}
