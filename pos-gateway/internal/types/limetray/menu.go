package limetray

// Menu represents the LimeTray menu response structure
type Menu struct {
	Success    string       `json:"success"`
	Message    string       `json:"message"`
	Categories []Category   `json:"categories"`
	Taxes      []Tax        `json:"taxes"`
	Charges    []Charge     `json:"charges"`
	Timings    []Timing     `json:"timings"`
}

// Category represents a menu category in LimeTray
type Category struct {
	CategoryID   string     `json:"categoryId"`
	CategoryName string     `json:"categoryName"`
	Rank         int        `json:"rank"`
	ProductList  []Product  `json:"productList,omitempty"`
	SubCategories []Category `json:"subCategories,omitempty"`
}

// Product represents a product/item in LimeTray
type Product struct {
	ProductID      string       `json:"productId"`
	ProductType    string       `json:"productType"`
	ProductName    string       `json:"productName"`
	ProductSkuList []ProductSku `json:"productSkuList"`
}

// ProductSku represents a product SKU in LimeTray
type ProductSku struct {
	ProductSkuID    string        `json:"productSkuId"`
	OutOfStock      bool          `json:"outOfStock"`
	ProductSkuPrice float64       `json:"productSkuPrice"`
	Addons          []AddonGroup  `json:"addons,omitempty"`
	Services        []Service     `json:"services,omitempty"`
}

// AddonGroup represents an addon group in LimeTray
type AddonGroup struct {
	AddonGroupID   string  `json:"addonGroupId"`
	AddonGroupName string  `json:"addonGroupName"`
	Min            int     `json:"min"`
	Max            int     `json:"max"`
	Addons         []Addon `json:"addons"`
}

// Addon represents an individual addon in LimeTray
type Addon struct {
	AddonID    string  `json:"addonId"`
	AddonName  string  `json:"addonName"`
	AddonPrice float64 `json:"addonPrice"`
	OutOfStock bool    `json:"outOfStock"`
}

// Service represents a service in LimeTray
type Service struct {
	ServiceID   string  `json:"serviceId"`
	ServiceName string  `json:"serviceName"`
	ServiceType string  `json:"serviceType"`
	Value       float64 `json:"value"`
}

// Tax represents a tax in LimeTray
type Tax struct {
	TaxChargeID   string  `json:"taxChargeId"`
	TaxChargeName string  `json:"taxChargeName"`
	TaxValue      float64 `json:"taxValue"`
	TaxType       string  `json:"taxType"`
}

// Charge represents a charge in LimeTray
type Charge struct {
	TaxChargeID   string  `json:"taxChargeId"`
	TaxChargeName string  `json:"taxChargeName"`
	TaxValue      float64 `json:"taxValue"`
	TaxType       string  `json:"taxType"`
}

// Timing represents timing information in LimeTray
type Timing struct {
	Day       string `json:"day"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
	Available bool   `json:"available"`
}
