package limetray

// Order represents the LimeTray order structure
type Order struct {
	OrderID          string        `json:"orderId"`
	RestaurantID     string        `json:"restaurantId"`
	OrderType        string        `json:"orderType"` // "delivery", "pickup", "dinein"
	OrderStatus      string        `json:"orderStatus"`
	Customer         Customer      `json:"customer"`
	Items            []OrderItem   `json:"items"`
	Payment          Payment       `json:"payment"`
	Pricing          Pricing       `json:"pricing"`
	DeliveryInfo     *DeliveryInfo `json:"deliveryInfo,omitempty"`
	SpecialInstructions string     `json:"specialInstructions,omitempty"`
	OrderTime        int64         `json:"orderTime"`
	EstimatedTime    int           `json:"estimatedTime"` // in minutes
}

// Customer represents customer information
type Customer struct {
	CustomerID   string  `json:"customerId"`
	Name         string  `json:"name"`
	Phone        string  `json:"phone"`
	Email        string  `json:"email,omitempty"`
	Address      *Address `json:"address,omitempty"`
}

// Address represents customer address
type Address struct {
	Street     string  `json:"street"`
	City       string  `json:"city"`
	State      string  `json:"state"`
	PostalCode string  `json:"postalCode"`
	Country    string  `json:"country"`
	Latitude   float64 `json:"latitude,omitempty"`
	Longitude  float64 `json:"longitude,omitempty"`
}

// OrderItem represents an item in the order
type OrderItem struct {
	ProductSkuID string           `json:"productSkuId"`
	ProductName  string           `json:"productName"`
	Quantity     int              `json:"quantity"`
	UnitPrice    float64          `json:"unitPrice"`
	TotalPrice   float64          `json:"totalPrice"`
	Addons       []OrderAddon     `json:"addons,omitempty"`
	Services     []OrderService   `json:"services,omitempty"`
	Instructions string           `json:"instructions,omitempty"`
}

// OrderAddon represents an addon in an order item
type OrderAddon struct {
	AddonID    string  `json:"addonId"`
	AddonName  string  `json:"addonName"`
	Quantity   int     `json:"quantity"`
	UnitPrice  float64 `json:"unitPrice"`
	TotalPrice float64 `json:"totalPrice"`
}

// OrderService represents a service applied to an order item
type OrderService struct {
	ServiceID   string  `json:"serviceId"`
	ServiceName string  `json:"serviceName"`
	ServiceType string  `json:"serviceType"`
	Value       float64 `json:"value"`
	Amount      float64 `json:"amount"`
}

// Payment represents payment information
type Payment struct {
	PaymentMethod string  `json:"paymentMethod"` // "cash", "card", "online", "wallet"
	PaymentStatus string  `json:"paymentStatus"` // "pending", "paid", "failed"
	Amount        float64 `json:"amount"`
	TransactionID string  `json:"transactionId,omitempty"`
}

// Pricing represents order pricing breakdown
type Pricing struct {
	SubTotal        float64      `json:"subTotal"`
	Taxes           []OrderTax   `json:"taxes"`
	Charges         []OrderCharge `json:"charges"`
	Discounts       []Discount   `json:"discounts,omitempty"`
	DeliveryCharge  float64      `json:"deliveryCharge"`
	PackingCharge   float64      `json:"packingCharge"`
	TotalAmount     float64      `json:"totalAmount"`
}

// OrderTax represents tax applied to the order
type OrderTax struct {
	TaxID     string  `json:"taxId"`
	TaxName   string  `json:"taxName"`
	TaxType   string  `json:"taxType"`
	TaxValue  float64 `json:"taxValue"`
	TaxAmount float64 `json:"taxAmount"`
}

// OrderCharge represents charges applied to the order
type OrderCharge struct {
	ChargeID     string  `json:"chargeId"`
	ChargeName   string  `json:"chargeName"`
	ChargeType   string  `json:"chargeType"`
	ChargeValue  float64 `json:"chargeValue"`
	ChargeAmount float64 `json:"chargeAmount"`
}

// Discount represents discounts applied to the order
type Discount struct {
	DiscountID   string  `json:"discountId"`
	DiscountName string  `json:"discountName"`
	DiscountType string  `json:"discountType"`
	DiscountValue float64 `json:"discountValue"`
	DiscountAmount float64 `json:"discountAmount"`
}

// DeliveryInfo represents delivery information
type DeliveryInfo struct {
	DeliveryTime     int64   `json:"deliveryTime"`
	DeliveryAddress  Address `json:"deliveryAddress"`
	DeliveryPartner  string  `json:"deliveryPartner,omitempty"`
	TrackingID       string  `json:"trackingId,omitempty"`
	EstimatedArrival int64   `json:"estimatedArrival"`
}

// OrderResponse represents the response from LimeTray order API
type OrderResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	OrderID   string `json:"orderId,omitempty"`
	ErrorCode string `json:"errorCode,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}
