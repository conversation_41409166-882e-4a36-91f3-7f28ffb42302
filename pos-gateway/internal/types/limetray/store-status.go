package limetray

// StoreStatus represents the LimeTray store status structure
type StoreStatus struct {
	RestaurantID     string            `json:"restaurantId"`
	IsOpen           bool              `json:"isOpen"`
	Status           string            `json:"status"` // "open", "closed", "busy"
	DeliveryStatus   DeliveryStatus    `json:"deliveryStatus"`
	PickupStatus     PickupStatus      `json:"pickupStatus"`
	DineInStatus     DineInStatus      `json:"dineInStatus"`
	EstimatedTime    EstimatedTime     `json:"estimatedTime"`
	Timestamp        int64             `json:"timestamp"`
	Reason           string            `json:"reason,omitempty"`
}

// DeliveryStatus represents delivery service status
type DeliveryStatus struct {
	Available     bool   `json:"available"`
	EstimatedTime int    `json:"estimatedTime"` // in minutes
	MinOrderValue float64 `json:"minOrderValue"`
	MaxOrderValue float64 `json:"maxOrderValue"`
}

// PickupStatus represents pickup service status
type PickupStatus struct {
	Available     bool `json:"available"`
	EstimatedTime int  `json:"estimatedTime"` // in minutes
}

// DineInStatus represents dine-in service status
type DineInStatus struct {
	Available     bool `json:"available"`
	TablesAvailable int `json:"tablesAvailable"`
	WaitTime      int  `json:"waitTime"` // in minutes
}

// EstimatedTime represents estimated preparation times
type EstimatedTime struct {
	Delivery int `json:"delivery"` // in minutes
	Pickup   int `json:"pickup"`   // in minutes
	DineIn   int `json:"dineIn"`   // in minutes
}

// StoreStatusResponse represents the response from LimeTray store status API
type StoreStatusResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	ErrorCode string `json:"errorCode,omitempty"`
	Data      interface{} `json:"data,omitempty"`
}
